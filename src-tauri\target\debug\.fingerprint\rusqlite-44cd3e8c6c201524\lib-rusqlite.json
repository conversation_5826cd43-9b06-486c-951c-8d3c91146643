{"rustc": 8148778215749587923, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"serde_json\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 3812117305541366783, "profile": 16690480377348987070, "path": 17953126443839344846, "deps": [[135859749300728301, "fallible_iterator", false, 2927258934063594775], [1001987408463263701, "fallible_streaming_iterator", false, 6949021778797727590], [5278979289915724395, "bitflags", false, 12133117519081013903], [8139673230253191462, "libsqlite3_sys", false, 7021228518896109427], [12051729459495111724, "hashlink", false, 11884874155514867874], [13902819013840624958, "smallvec", false, 3135082089122591360]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-44cd3e8c6c201524\\dep-lib-rusqlite"}}], "rustflags": [], "metadata": 10818493136143991971, "config": 2202906307356721367, "compile_kind": 0}