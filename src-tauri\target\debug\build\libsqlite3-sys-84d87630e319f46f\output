cargo:rerun-if-changed=sqlite3/sqlite3.c
cargo:rerun-if-changed=sqlite3/wasm32-wasi-vfs.c
cargo:rerun-if-env-changed=SQLITE_MAX_VARIABLE_NUMBER
cargo:rerun-if-env-changed=SQLITE_MAX_EXPR_DEPTH
cargo:rerun-if-env-changed=LIBSQLITE3_FLAGS
OUT_DIR = Some(E:\前端\auto-cursor\src-tauri\target\debug\build\libsqlite3-sys-84d87630e319f46f\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(E:\前端\auto-cursor\src-tauri\target\debug\deps;E:\前端\auto-cursor\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;E:\前端\auto-cursor\node_modules\.bin;E:\nvm\v20.19.4\node_modules\pnpm\dist\node-gyp-bin;D:\python365\Scripts\;D:\python365\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\Git\cmd;E:\微信web开发者工具\dll;E:\微信web开发者工具\wechat\dll;E:\nvm;E:\node;C:\Program Files (x86)\NetSarang\Xftp 7\;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64;D:\New Folder\SAC\x64;D:\New Folder\SAC\x32;E:\Android\SDK\platform-tools;E:\Android\SDK\emulator;E:\Android\SDK\tools;E:\Android\SDK\tools\bin;C:\Program Files\Java\jdk-17\bin;E:\gradle-8.3-all\gradle-8.3\bin;E:\Bandizip\;C:\Program Files\MySQL\MySQL Server 5.7\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;E:\Microsoft VS Code\bin;E:\nvm;E:\node;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64;E:\Android\SDK\platform-tools;C:\Program Files\MySQL\MySQL Server 5.7\bin;D:\cursor\resources\app\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
sqlite3.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=D:\vsIde\VC\Tools\MSVC\14.41.34120\atlmfc\lib\x64
cargo:rustc-link-lib=static=sqlite3
cargo:rustc-link-search=native=E:\前端\auto-cursor\src-tauri\target\debug\build\libsqlite3-sys-84d87630e319f46f\out
cargo:lib_dir=E:\前端\auto-cursor\src-tauri\target\debug\build\libsqlite3-sys-84d87630e319f46f\out
