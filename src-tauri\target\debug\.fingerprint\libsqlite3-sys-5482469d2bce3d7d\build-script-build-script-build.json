{"rustc": 8148778215749587923, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 13708040221295731214, "profile": 8861558584828204938, "path": 12623045606011371353, "deps": [[1123375732303314408, "cc", false, 4072306887917937196], [7218239883571173546, "pkg_config", false, 17604531559466150273], [8444855962349136217, "vcpkg", false, 12047536550057434737]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-5482469d2bce3d7d\\dep-build-script-build-script-build"}}], "rustflags": [], "metadata": 14430067081211154946, "config": 2202906307356721367, "compile_kind": 0}